'use client';

import React, { useCallback } from 'react';
import HtmlMockupNodeComponentFromFile from './HtmlMockupNode';
import { ReactFlow } from '@xyflow/react';
import { MiniMap, Controls, Background, addEdge, ReactFlowProvider, BackgroundVariant, Panel } from '@xyflow/react';
import type { Connection, Edge, OnNodesChange, OnEdgesChange } from '@xyflow/react';
import { HtmlMockupNode, Page } from '@/lib/types';
import PromptControlArea from '../PromptControlArea';
import PageManager from './PageManager';

import '@xyflow/react/dist/style.css';
import '@xyflow/react/dist/base.css';

interface FlowCanvasProps {
  nodes: HtmlMockupNode[];
  edges?: Edge[];
  onNodesChange: OnNodesChange<HtmlMockupNode>;
  onEdgesChange?: OnEdgesChange<Edge>;
  setEdges?: React.Dispatch<React.SetStateAction<Edge<any>[]>>;
  addMockupNode: (prompt: string) => void;
  deselectNode: () => void;
  onRename?: (nodeId: string, name: string) => void;
  onViewCode?: (nodeId: string) => void;
  // Page management props
  pages: Page[];
  currentPageId: string;
  onCreatePage: () => void;
  onDeletePage: (pageId: string) => void;
  onRenamePage: (pageId: string, newName: string) => void;
  onSwitchPage: (pageId: string) => void;
}

const nodeTypes = {
  htmlMockupNode: HtmlMockupNodeComponentFromFile,
};

const FlowCanvas = ({
  nodes,
  edges = [],
  onNodesChange,
  onEdgesChange = () => {},
  setEdges = () => {},
  addMockupNode,
  deselectNode,
  onRename,
  onViewCode,
  pages,
  currentPageId,
  onCreatePage,
  onDeletePage,
  onRenamePage,
  onSwitchPage,
}: FlowCanvasProps) => {
  const onConnect = useCallback(
    (params: Connection | Edge) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const handlePaneClick = useCallback(() => {
    deselectNode();
  }, [deselectNode]);

  // Update nodes with toolbar callbacks
  const nodesWithCallbacks = nodes.map(node => ({
    ...node,
    data: {
      ...node.data,
      onRename,
      onViewCode,
      onSelect: (nodeId: string | null) => {
        if (nodeId === null) {
          deselectNode();
        } else {
          node.data.onSelect?.(nodeId);
        }
      },
    },
  }));

  const proOptions = { hideAttribution: true };


  return (
    <div className="w-full flex-grow min-h-[400px] bg-slate-100" style={{ width: '100vw', height: '100%' }}>
      <ReactFlowProvider>
        <ReactFlow
          nodes={nodesWithCallbacks}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onPaneClick={handlePaneClick}
          fitView
          proOptions={proOptions}
          minZoom={0.1}
          maxZoom={1.5}
          nodeTypes={nodeTypes}
        >
          <Panel position="top-center" className="w-full max-w-2xl">
            <PromptControlArea addMockupNode={addMockupNode} />
          </Panel>
          <PageManager
            pages={pages}
            currentPageId={currentPageId}
            onCreatePage={onCreatePage}
            onDeletePage={onDeletePage}
            onRenamePage={onRenamePage}
            onSwitchPage={onSwitchPage}
          />
          <Controls />
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
        </ReactFlow>
      </ReactFlowProvider>
    </div>
  );
};

export default FlowCanvas;