export function generateMockupHtml(prompt: string): string {
  switch (prompt.toLowerCase()) {
    case "button":
      return `<div class="flex flex-row gap-4">
      <div class="mobilescreen flex flex-col items-center justify-between p-6 bg-gradient-to-br from-blue-100 to-purple-100">
  <div class="w-full flex justify-end">
    <a href="#" class="text-gray-600 text-sm font-semibold">Skip</a>
  </div>
  <div class="flex flex-col items-center text-center flex-grow justify-center">
    <img src="https://picsum.photos/id/171/300/300" alt="Welcome illustration" class="w-64 h-64 object-cover rounded-full shadow-lg mb-8"/>
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Welcome Aboard!</h1>
    <p class="text-md text-gray-600 mb-8 px-4">We're excited to have you join us. Let's get you set up in just a few steps.</p>
  </div>
  <div class="w-full">
    <div class="w-full h-2 bg-gray-300 rounded-full mb-4">
      <div class="w-1/3 h-full bg-blue-600 rounded-full"></div>
    </div>
    <p class="text-sm text-gray-600 text-center mb-4">Step 1 of 3</p>
    <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold text-lg shadow-md">Get Started</button>
  </div>
</div>
<div class="mobilescreen flex flex-col items-center justify-between p-6 bg-gradient-to-br from-purple-100 to-pink-100">
    <div class="w-full flex justify-end">
    <a href="#" class="text-gray-600 text-sm font-semibold">Skip</a>
  </div>
  <div class="flex flex-col items-center text-center flex-grow justify-center">
    <Lucide.ShieldCheck class="w-24 h-24 text-purple-600 mb-8"/>
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Let's Set Up Permissions</h1>
    <p class="text-md text-gray-600 mb-8 px-4">To give you the best experience, we need a few permissions. You can manage these anytime in settings.</p>
    <div class="w-full max-w-sm text-left space-y-4">
      <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
        <Lucide.MapPin class="w-6 h-6 text-blue-500 mr-4"/>
        <div>
          <p class="font-semibold text-gray-700">Location Access</p>
          <p class="text-sm text-gray-500">Needed for personalized features.</p>
        </div>
      </div>
      <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
        <Lucide.Bell class="w-6 h-6 text-green-500 mr-4"/>
        <div>
          <p class="font-semibold text-gray-700">Notifications</p>
          <p class="text-sm text-gray-500">Stay updated on important activity.</p>
        </div>
      </div>
      <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
        <Lucide.Camera class="w-6 h-6 text-red-500 mr-4"/>
        <div>
          <p class="font-semibold text-gray-700">Camera Access</p>
          <p class="text-sm text-gray-500">For profile pictures & sharing photos.</p>
        </div>
      </div>
    </div>
  </div>
  <div class="w-full">
     <div class="w-full h-2 bg-gray-300 rounded-full mb-4">
      <div class="w-2/3 h-full bg-purple-600 rounded-full"></div>
    </div>
     <p class="text-sm text-gray-600 text-center mb-4">Step 2 of 3</p>
    <button class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold text-lg shadow-md">Allow Permissions</button>
  </div>
</div>
<div class="mobilescreen flex flex-col items-center justify-between p-6 bg-gradient-to-br from-pink-100 to-orange-100">
   <div class="w-full flex justify-end">
    <a href="#" class="text-gray-600 text-sm font-semibold">Skip</a>
  </div>
  <div class="flex flex-col items-center text-center flex-grow justify-center w-full">
    <Lucide.User class="w-24 h-24 text-pink-600 mb-8"/>
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Complete Your Profile</h1>
    <p class="text-md text-gray-600 mb-8 px-4">Just a few details to finish setting up your account.</p>
    <div class="w-full max-w-sm space-y-6">
      <div>
        <label htmlFor="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
        <input type="text" id="name" placeholder="John Doe" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"/>
      </div>
      <div>
        <label htmlFor="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
        <input type="email" id="email" placeholder="<EMAIL>" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"/>
      </div>
      <div>
        <label htmlFor="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
        <input type="password" id="password" placeholder="Enter secure password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"/>
      </div>
    </div>
  </div>
   <div class="w-full">
     <div class="w-full h-2 bg-gray-300 rounded-full mb-4">
      <div class="w-full h-full bg-pink-600 rounded-full"></div>
    </div>
     <p class="text-sm text-gray-600 text-center mb-4">Step 3 of 3</p>
    <button class="w-full bg-pink-600 text-white py-3 rounded-lg font-semibold text-lg shadow-md">Finish Setup</button>
  </div>
</div>
</div>`;
    case "card":
      return `<div class='bg-white shadow-lg rounded-lg p-4 border border-gray-200'><h2>Generated Card</h2><p>Content for: ${prompt}</p></div>`;
    default:
      return `<div class='p-4 border border-blue-500 rounded-md bg-blue-100'><h1>Generated HTML for: ${prompt}</h1><p>This is a default mockup for an unrecognized prompt.</p></div>`;
  }
}