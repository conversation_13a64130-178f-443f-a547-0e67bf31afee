import { Node, Edge } from '@xyflow/react';

export interface HtmlMockupNodeData {
  htmlString: string;
  onDelete: (nodeId: string) => void;
  onSelect?: (nodeId: string | null) => void;  // Allow null for deselection
  onRename?: (nodeId: string, name: string) => void;  // Add rename callback
  onCopyToFigma?: (nodeId: string) => void; // Replaces onViewCode
  isSelected?: boolean;  // Add selection state
  name?: string;  // Adding name field early since we'll need it soon
  dimensions?: {  // Adding dimensions early since we'll need it soon
    width: number;
    height: number;
  };
  [key: string]: any; // Add index signature to satisfy Node<T> constraint
  // any other data your custom node might need
}

export type HtmlMockupNode = Node<HtmlMockupNodeData, 'htmlMockupNode'>;

// You might also want a general AppNode type if you plan other node types
// For now, HtmlMockupNode is the focus.
// export type AppNode = HtmlMockupNode; // or a union type later