import { Node, Edge } from '@xyflow/react';

export interface HtmlMockupNodeData {
  htmlString: string;
  onDelete: (nodeId: string) => void;
  onSelect?: (nodeId: string | null) => void;  // Allow null for deselection
  onRename?: (nodeId: string, name: string) => void;  // Add rename callback
  onCopyToFigma?: (nodeId: string) => void; // Replaces onViewCode
  isSelected?: boolean;  // Add selection state
  name?: string;  // Adding name field early since we'll need it soon
  dimensions?: {  // Adding dimensions early since we'll need it soon
    width: number;
    height: number;
  };
  [key: string]: any; // Add index signature to satisfy Node<T> constraint
  // any other data your custom node might need
}

export type HtmlMockupNode = Node<HtmlMockupNodeData, 'htmlMockupNode'>;

// You might also want a general AppNode type if you plan other node types
// For now, HtmlMockupNode is the focus.
// export type AppNode = HtmlMockupNode; // or a union type later

// Page Management Types
export interface Page {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PageData {
  page: Page;
  nodes: HtmlMockupNode[];
  edges: Edge[];
}

export interface PageManagerState {
  pages: Page[];
  currentPageId: string;
  pageData: Record<string, { nodes: HtmlMockupNode[]; edges: Edge[] }>;
}

export interface PageManagerActions {
  createPage: (name?: string) => string;
  deletePage: (pageId: string) => void;
  renamePage: (pageId: string, newName: string) => void;
  switchToPage: (pageId: string) => void;
  getCurrentPage: () => Page | undefined;
  getCurrentPageData: () => { nodes: HtmlMockupNode[]; edges: Edge[] };
  updateCurrentPageData: (nodes: HtmlMockupNode[], edges: Edge[]) => void;
}