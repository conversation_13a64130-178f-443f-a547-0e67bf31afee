/**
 * @jest-environment jsdom
 */

import { renderHook, act } from '@testing-library/react';
import { usePageManager } from '@/hooks/usePageManager';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('usePageManager', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
  });

  it('should initialize with default page when no saved data', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    expect(result.current.pages).toHaveLength(1);
    expect(result.current.pages[0].name).toBe('Page 1');
    expect(result.current.pages[0].id).toBe('default');
    expect(result.current.currentPageId).toBe('default');
  });

  it('should create a new page', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    act(() => {
      const newPageId = result.current.createPage('Test Page');
      expect(newPageId).toBeDefined();
    });

    expect(result.current.pages).toHaveLength(2);
    expect(result.current.pages[1].name).toBe('Test Page');
  });

  it('should delete a page', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    let newPageId: string;
    act(() => {
      newPageId = result.current.createPage('Test Page');
    });

    act(() => {
      result.current.deletePage(newPageId);
    });

    expect(result.current.pages).toHaveLength(1);
    expect(result.current.pages[0].name).toBe('Page 1');
  });

  it('should not delete the last page', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    act(() => {
      result.current.deletePage('default');
    });

    expect(result.current.pages).toHaveLength(1);
  });

  it('should rename a page', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    act(() => {
      result.current.renamePage('default', 'Renamed Page');
    });

    expect(result.current.pages[0].name).toBe('Renamed Page');
  });

  it('should switch to a page', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    let newPageId: string;
    act(() => {
      newPageId = result.current.createPage('Test Page');
    });

    act(() => {
      result.current.switchToPage(newPageId);
    });

    expect(result.current.currentPageId).toBe(newPageId);
  });

  it('should get current page data', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    const pageData = result.current.getCurrentPageData();
    expect(pageData).toEqual({ nodes: [], edges: [] });
  });

  it('should update current page data', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => usePageManager());

    const mockNodes = [{ id: '1', type: 'htmlMockupNode', position: { x: 0, y: 0 }, data: {} }] as any;
    const mockEdges = [{ id: 'e1', source: '1', target: '2' }] as any;

    act(() => {
      result.current.updateCurrentPageData(mockNodes, mockEdges);
    });

    const pageData = result.current.getCurrentPageData();
    expect(pageData.nodes).toEqual(mockNodes);
    expect(pageData.edges).toEqual(mockEdges);
  });
});
