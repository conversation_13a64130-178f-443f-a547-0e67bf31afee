'use client';

import FlowCanvas from '@/components/canvas/FlowCanvas';
import { useMockupNodes } from '@/hooks/useMockupNodes';
import { Toaster } from 'sonner';

import '@/styles/node.css';

export default function Home() {
  const {
    nodes,
    onNodesChange,
    addMockupNode,
    deselectAllNodes,
    renameNode,
  } = useMockupNodes();

  return (
    <main className="flex min-h-screen flex-col h-[calc(100vh)]">
      <FlowCanvas
        nodes={nodes}
        edges={[]}
        onNodesChange={onNodesChange}
        onEdgesChange={() => {}}
        setEdges={() => {}}
        addMockupNode={addMockupNode}
        deselectNode={deselectAllNodes}
        onRename={renameNode}
      />
      <Toaster />
    </main>
  );
}
