'use client';

import { useCallback } from 'react';
import FlowCanvas from '@/components/canvas/FlowCanvas';
import { useMockupNodes } from '@/hooks/useMockupNodes';
import { usePageManager } from '@/hooks/usePageManager';
import { Toaster } from 'sonner';

import '@/styles/node.css';

export default function Home() {
  const {
    pages,
    currentPageId,
    createPage,
    deletePage,
    renamePage,
    switchToPage,
    getCurrentPageData,
    updateCurrentPageData,
  } = usePageManager();

  const currentPageData = getCurrentPageData();

  const handleDataChange = useCallback((nodes: any[], edges: any[]) => {
    updateCurrentPageData(nodes, edges);
  }, [updateCurrentPageData]);

  const {
    nodes,
    edges,
    onNodesChange,
    addMockupNode,
    deselectAllNodes,
    renameNode,
  } = useMockupNodes({
    initialNodes: currentPageData.nodes,
    initialEdges: currentPageData.edges,
    onDataChange: handleDataChange,
  });

  return (
    <main className="flex min-h-screen flex-col h-[calc(100vh)]">
      <FlowCanvas
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={() => {}}
        setEdges={() => {}}
        addMockupNode={addMockupNode}
        deselectNode={deselectAllNodes}
        onRename={renameNode}
        pages={pages}
        currentPageId={currentPageId}
        onCreatePage={createPage}
        onDeletePage={deletePage}
        onRenamePage={renamePage}
        onSwitchPage={switchToPage}
      />
      <Toaster />
    </main>
  );
}
