@import '@xyflow/react/dist/style.css';
@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@source inline('{bg,text}-gradient-to-br from-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white}-{50,{100..900..100},950} to-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white}-{50,{100..900..100},950}');

@source inline('{bg,text}-gradient-to-b from-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white}-{50,{100..900..100},950} to-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white}-{50,{100..900..100},950}');

@source inline('{bg,text}-gradient-to-br from-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white} to-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white}');

@source inline('{bg,text}-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white}-{50,{100..900..100},950}');

/* AI-Generated Safelist for Dynamic Mockups */

/* Spacing */
@source inline('{p,m,space-x,space-y,scroll-m,scroll-p}-{0,px,0.5,1,1.5,2,2.5,3,3.5,4,5,6,7,8,9,10,11,12,14,16,20,24,28,32,36,40,44,48,52,56,60,64,72,80,96}');
@source inline('{p,m,space-x,space-y,scroll-m,scroll-p}{t,r,b,l,x,y}-{0,px,0.5,1,1.5,2,2.5,3,3.5,4,5,6,7,8,9,10,11,12,14,16,20,24,28,32,36,40,44,48,52,56,60,64,72,80,96}');
@source inline('gap-{0,px,0.5,1,1.5,2,2.5,3,3.5,4,5,6,7,8,9,10,11,12,14,16,20,24,28,32,36,40,44,48,52,56,60,64,72,80,96}');
@source inline('gap-{x,y}-{0,px,0.5,1,1.5,2,2.5,3,3.5,4,5,6,7,8,9,10,11,12,14,16,20,24,28,32,36,40,44,48,52,56,60,64,72,80,96}');

/* Sizing */
@source inline('{w,h}-{0,auto,px,0.5,1,1.5,2,2.5,3,3.5,4,5,6,7,8,9,10,11,12,14,16,20,24,28,32,36,40,44,48,52,56,60,64,72,80,96,full,screen,svw,lvw,dvw,min,max,fit}');
@source inline('{w,h}-{1/2,1/3,2/3,1/4,2/4,3/4,1/5,2/5,3/5,4/5,1/6,5/6,1/12,2/12,3/12,4/12,5/12,6/12,7/12,8/12,9/12,10/12,11/12}');
@source inline('{min-w,max-w,min-h,max-h}-{0,full,min,max,fit,screen,px,0.5,1,1.5,2,2.5,3,3.5,4,5,6,7,8,9,10,11,12,14,16,20,24,28,32,36,40,44,48,52,56,60,64,72,80,96}');

/* Typography */
@source inline('text-{xs,sm,base,lg,xl,2xl,3xl,4xl,5xl,6xl,7xl,8xl,9xl}');
@source inline('font-{thin,extralight,light,normal,medium,semibold,bold,extrabold,black}');
@source inline('leading-{3,4,5,6,7,8,9,10,none,tight,snug,normal,relaxed,loose}');
@source inline('tracking-{tighter,tight,normal,wide,wider,widest}');
@source inline('text-{left,center,right,justify,start,end}');
@source inline('{italic,not-italic,underline,overline,line-through,no-underline,uppercase,lowercase,capitalize,normal-case}');
@source inline('align-{baseline,top,middle,bottom,text-top,text-bottom,sub,super}');
@source inline('whitespace-{normal,nowrap,pre,pre-line,pre-wrap,break-spaces}');
@source inline('break-{normal,words,all,keep}');

/* Colors */
@source inline('{border,divide,ring,ring-offset,shadow,accent,bg,text,decoration,outline}-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white,black,transparent}');
@source inline('{border,divide,ring,ring-offset,shadow,accent,bg,text,decoration,outline}-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white,black,transparent}-{50,{100..900..100},950}');
@source inline('{from,to,via}-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white,black,transparent}');
@source inline('{from,to,via}-{slate,gray,zinc,neutral,stone,red,orange,amber,yellow,lime,green,emerald,teal,cyan,sky,blue,indigo,violet,purple,fuchsia,pink,rose,white,black,transparent}-{50,{100..900..100},950}');
@source inline('bg-gradient-to-{t,tr,r,br,b,bl,l,tl}');

/* Borders */
@source inline('border-{0,1,2,4,8}');
@source inline('border-{x,y,s,e,t,r,b,l}-{0,1,2,4,8}');
@source inline('rounded-{none,sm,md,lg,xl,2xl,3xl,full}');
@source inline('rounded-{s,e,t,r,b,l,ss,se,es,ee,ts,te,rs,re,bs,be,ls,le,start,end,t,r,b,l,tl,tr,br,bl}-{none,sm,md,lg,xl,2xl,3xl,full}');

/* Effects */
@source inline('shadow-{sm,md,lg,xl,2xl,inner,none}');
@source inline('opacity-{0..100..5}');
@source inline('blur-{none,sm,md,lg,xl,2xl,3xl}');

/* Flexbox & Grid */
@source inline('{flex,grid}-{row,row-reverse,col,col-reverse}');
@source inline('flex-{wrap,wrap-reverse,nowrap}');
@source inline('{items,self}-{start,end,center,baseline,stretch,auto}');
@source inline('{justify,content}-{start,end,center,between,around,evenly,stretch}');
@source inline('flex-{1,auto,initial,none,grow,shrink}');
@source inline('flex-{grow,shrink}-{0,1}');
@source inline('grid-cols-{1..12,none}');
@source inline('grid-rows-{1..6,none}');
@source inline('col-span-{1..12,full}');
@source inline('row-span-{1..6,full}');
@source inline('col-start-{1..13,auto}');
@source inline('row-start-{1..7,auto}');
@source inline('col-end-{1..13,auto}');
@source inline('row-end-{1..7,auto}');
@source inline('order-{1..12,first,last,none}');

/* Other */
@source inline('cursor-{auto,default,pointer,wait,text,move,help,not-allowed,none,context-menu,progress,cell,crosshair,vertical-text,alias,copy,no-drop,grab,grabbing,all-scroll,col-resize,row-resize,n-resize,e-resize,s-resize,w-resize,ne-resize,nw-resize,se-resize,sw-resize,ew-resize,ns-resize,nesw-resize,nwse-resize,zoom-in,zoom-out}');

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  .mobilescreen {
    @apply w-[400px];
  }
  .react-flow__panel {
    padding: 5px 10px;
    background: white;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
  }

  /* .react-flow__node {
    @apply overflow-scroll;
  } */
}
