'use client';

import { useCallback, useEffect } from 'react';
import { useNodesState, Edge } from '@xyflow/react';
import { HtmlMockupNode } from '../lib/types';
import { generateMockupHtml } from '../lib/mockupGenerator';
import { toast } from 'sonner';
import { elementToSVG, inlineResources } from 'dom-to-svg';

interface UseMockupNodesProps {
  initialNodes: HtmlMockupNode[];
  initialEdges: Edge[];
  onDataChange: (nodes: HtmlMockupNode[], edges: Edge[]) => void;
}

export function useMockupNodes({ initialNodes, initialEdges, onDataChange }: UseMockupNodesProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState<HtmlMockupNode>(initialNodes);

  // Update nodes when initialNodes change (page switch)
  useEffect(() => {
    setNodes(initialNodes);
  }, [initialNodes, setNodes]);

  // Notify parent when nodes change
  useEffect(() => {
    onDataChange(nodes, initialEdges);
  }, [nodes, initialEdges, onDataChange]);

  const addMockupNode = useCallback(
    (prompt: string) => {
      // Generate HTML mockup from the prompt
      const htmlString = generateMockupHtml(prompt);

      const newNode: HtmlMockupNode = {
        id: `node-${Date.now()}`,
        type: 'htmlMockupNode',
        position: { x: Math.random() * 500, y: Math.random() * 500 },
        data: {
          htmlString,
          onDelete: () => {}, // Will be set in nodesWithCallbacks
          onSelect: () => {}, // Will be set in nodesWithCallbacks
          onRename: () => {}, // Will be set in nodesWithCallbacks
          onCopyToFigma: () => {}, // Will be set in nodesWithCallbacks
          name: `Mockup: ${prompt}`, // Use the prompt as part of the default name
          isSelected: false,
        },
      };

      setNodes((nds) => [...nds, newNode]);
      toast.success('Added new mockup node', {
        description: `Generated from prompt: "${prompt}"`,
      });
    },
    [setNodes]
  );

  const deleteMockupNode = useCallback(
    (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      toast.success('Deleted mockup node');
    },
    [setNodes]
  );

  const selectNode = useCallback(
    (nodeId: string | null) => {
      setNodes((nds) =>
        nds.map((node) => ({
          ...node,
          data: {
            ...node.data,
            isSelected: nodeId !== null && node.id === nodeId,
          },
        }))
      );
    },
    [setNodes]
  );

  const renameNode = useCallback(
    (nodeId: string, name: string) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId
            ? {
                ...node,
                data: {
                  ...node.data,
                  name,
                },
              }
            : node
        )
      );
      toast.success('Renamed mockup node');
    },
    [setNodes]
  );

  const copyNodeToFigma = useCallback(async (nodeId: string) => {
    const nodeElement = document.getElementById(`mockup-node-content-${nodeId}`);

    if (!nodeElement) {
      toast.error('Could not find node content to copy.');
      return;
    }

    try {
      const svgDocument = await elementToSVG(nodeElement);
      await inlineResources(svgDocument.documentElement);
      const svgString = new XMLSerializer().serializeToString(svgDocument);
      
      await navigator.clipboard.writeText(svgString);
      
      toast.success('Copied to clipboard!', {
        description: 'You can now paste it into Figma.',
      });
    } catch (error) {
      console.error('Failed to copy to Figma:', error);
      toast.error('Failed to copy to Figma.');
    }
  }, []);

  const deselectAllNodes = useCallback(() => {
    setNodes((nds) =>
      nds.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isSelected: false,
        },
      }))
    );
  }, [setNodes]);

  // Update nodes with callbacks
  const nodesWithCallbacks = nodes.map(node => ({
    ...node,
    data: {
      ...node.data,
      onDelete: deleteMockupNode,
      onSelect: selectNode,
      onRename: renameNode,
      onCopyToFigma: copyNodeToFigma,
    },
  }));

  return {
    nodes: nodesWithCallbacks,
    edges: initialEdges,
    setNodes,
    onNodesChange,
    addMockupNode,
    deleteMockupNode,
    selectNode,
    renameNode,
    copyNodeToFigma,
    deselectAllNodes,
  };
}