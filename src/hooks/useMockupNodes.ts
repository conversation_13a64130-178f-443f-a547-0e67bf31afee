'use client';

import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useNodesState, Edge } from '@xyflow/react';
import { HtmlMockupNode, PageManagerState, PageManagerActions } from '../lib/types';
import { generateMockupHtml } from '../lib/mockupGenerator';
import { toast } from 'sonner';
import { elementToSVG, inlineResources } from 'dom-to-svg';

interface UseMockupNodesProps {
  initialNodes: HtmlMockupNode[];
  initialEdges: Edge[];
  pageManager: PageManagerState & PageManagerActions;
}

export function useMockupNodes({ initialNodes, initialEdges, pageManager }: UseMockupNodesProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState<HtmlMockupNode>(initialNodes);
  const isLoadingFromPage = useRef(false);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update nodes when initialNodes change (page switch)
  useEffect(() => {
    isLoadingFromPage.current = true;
    setNodes(initialNodes);
    // Reset the flag after a short delay to ensure state has updated
    setTimeout(() => {
      isLoadingFromPage.current = false;
    }, 100);
  }, [initialNodes, setNodes]);

  // Debounced save to page manager - saves after 500ms of no changes
  useEffect(() => {
    // Don't save if we're currently loading from page data
    if (isLoadingFromPage.current) {
      console.log('🔄 Skipping save - loading from page data');
      return;
    }

    console.log('💾 Setting up debounced save for', nodes.length, 'nodes');

    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Set new timeout to save after 500ms
    saveTimeoutRef.current = setTimeout(() => {
      console.log('💾 Saving to page manager:', nodes.length, 'nodes');
      pageManager.updateCurrentPageData(nodes, initialEdges);
    }, 500);

    // Cleanup timeout on unmount
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [nodes, initialEdges, pageManager.updateCurrentPageData]);



  const addMockupNode = useCallback(
    (prompt: string) => {
      // Generate HTML mockup from the prompt
      const htmlString = generateMockupHtml(prompt);

      const newNode: HtmlMockupNode = {
        id: `node-${Date.now()}`,
        type: 'htmlMockupNode',
        position: { x: Math.random() * 500, y: Math.random() * 500 },
        data: {
          htmlString,
          onDelete: () => {}, // Will be set in nodesWithCallbacks
          onSelect: () => {}, // Will be set in nodesWithCallbacks
          onRename: () => {}, // Will be set in nodesWithCallbacks
          onCopyToFigma: () => {}, // Will be set in nodesWithCallbacks
          name: `Mockup: ${prompt}`, // Use the prompt as part of the default name
          isSelected: false,
        },
      };

      setNodes((nds) => [...nds, newNode]);
      toast.success('Added new mockup node', {
        description: `Generated from prompt: "${prompt}"`,
      });
    },
    [setNodes]
  );

  const deleteMockupNode = useCallback(
    (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
      toast.success('Deleted mockup node');
    },
    [setNodes]
  );

  const selectNode = useCallback(
    (nodeId: string | null) => {
      setNodes((nds) =>
        nds.map((node) => ({
          ...node,
          data: {
            ...node.data,
            isSelected: nodeId !== null && node.id === nodeId,
          },
        }))
      );
    },
    [setNodes]
  );

  const renameNode = useCallback(
    (nodeId: string, name: string) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId
            ? {
                ...node,
                data: {
                  ...node.data,
                  name,
                },
              }
            : node
        )
      );
      toast.success('Renamed mockup node');
    },
    [setNodes]
  );

  const copyNodeToFigma = useCallback(async (nodeId: string) => {
    const nodeElement = document.getElementById(`mockup-node-content-${nodeId}`);

    if (!nodeElement) {
      toast.error('Could not find node content to copy.');
      return;
    }

    try {
      const svgDocument = await elementToSVG(nodeElement);
      await inlineResources(svgDocument.documentElement);
      const svgString = new XMLSerializer().serializeToString(svgDocument);
      
      await navigator.clipboard.writeText(svgString);
      
      toast.success('Copied to clipboard!', {
        description: 'You can now paste it into Figma.',
      });
    } catch (error) {
      console.error('Failed to copy to Figma:', error);
      toast.error('Failed to copy to Figma.');
    }
  }, []);

  const deselectAllNodes = useCallback(() => {
    setNodes((nds) =>
      nds.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isSelected: false,
        },
      }))
    );
  }, [setNodes]);

  // Memoize nodes with callbacks to prevent infinite re-renders
  const nodesWithCallbacks = useMemo(() => {
    return nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        onDelete: deleteMockupNode,
        onSelect: selectNode,
        onRename: renameNode,
        onCopyToFigma: copyNodeToFigma,
      },
    }));
  }, [nodes, deleteMockupNode, selectNode, renameNode, copyNodeToFigma]);

  return {
    nodes: nodesWithCallbacks,
    edges: initialEdges,
    setNodes,
    onNodesChange,
    addMockupNode,
    deleteMockupNode,
    selectNode,
    renameNode,
    copyNodeToFigma,
    deselectAllNodes,
  };
}