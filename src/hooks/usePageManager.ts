'use client';

import { useState, useEffect, useCallback } from 'react';
import { Edge } from '@xyflow/react';
import { Page, PageManagerState, PageManagerActions, HtmlMockupNode } from '@/lib/types';
import { toast } from 'sonner';

const PAGES_STORAGE_KEY = 'mockup-pages';
const PAGE_DATA_STORAGE_KEY = 'mockup-page-data';

const createDefaultPage = (): Page => ({
  id: 'default',
  name: 'Page 1',
  createdAt: new Date(),
  updatedAt: new Date(),
});

const generatePageId = (): string => `page-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export function usePageManager(): PageManagerState & PageManagerActions {
  const [pages, setPages] = useState<Page[]>([]);
  const [currentPageId, setCurrentPageId] = useState<string>('default');
  const [pageData, setPageData] = useState<Record<string, { nodes: HtmlMockupNode[]; edges: Edge[] }>>({});

  // Load pages and page data from localStorage on mount
  useEffect(() => {
    try {
      const savedPages = localStorage.getItem(PAGES_STORAGE_KEY);
      const savedPageData = localStorage.getItem(PAGE_DATA_STORAGE_KEY);

      if (savedPages) {
        const parsedPages: Page[] = JSON.parse(savedPages).map((page: any) => ({
          ...page,
          createdAt: new Date(page.createdAt),
          updatedAt: new Date(page.updatedAt),
        }));
        setPages(parsedPages);
        
        // Set current page to the first page or default
        if (parsedPages.length > 0) {
          setCurrentPageId(parsedPages[0].id);
        }
      } else {
        // Create default page if none exist
        const defaultPage = createDefaultPage();
        setPages([defaultPage]);
        setCurrentPageId(defaultPage.id);
      }

      if (savedPageData) {
        setPageData(JSON.parse(savedPageData));
      } else {
        // Initialize with empty data for default page
        setPageData({ default: { nodes: [], edges: [] } });
      }
    } catch (error) {
      console.error('Failed to load pages from localStorage:', error);
      toast.error('Failed to load saved pages');
      
      // Fallback to default page
      const defaultPage = createDefaultPage();
      setPages([defaultPage]);
      setCurrentPageId(defaultPage.id);
      setPageData({ default: { nodes: [], edges: [] } });
    }
  }, []);

  // Save pages to localStorage whenever they change
  useEffect(() => {
    if (pages.length > 0) {
      localStorage.setItem(PAGES_STORAGE_KEY, JSON.stringify(pages));
    }
  }, [pages]);

  // Save page data to localStorage whenever it changes
  useEffect(() => {
    if (Object.keys(pageData).length > 0) {
      localStorage.setItem(PAGE_DATA_STORAGE_KEY, JSON.stringify(pageData));
    }
  }, [pageData]);

  const createPage = useCallback((name?: string): string => {
    const newPageId = generatePageId();
    const pageNumber = pages.length + 1;
    const newPage: Page = {
      id: newPageId,
      name: name || `Page ${pageNumber}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setPages(prev => [...prev, newPage]);
    setPageData(prev => ({
      ...prev,
      [newPageId]: { nodes: [], edges: [] }
    }));

    toast.success(`Created new page: ${newPage.name}`);
    return newPageId;
  }, [pages.length]);

  const deletePage = useCallback((pageId: string) => {
    if (pages.length <= 1) {
      toast.error('Cannot delete the last page');
      return;
    }

    const pageToDelete = pages.find(p => p.id === pageId);
    if (!pageToDelete) {
      toast.error('Page not found');
      return;
    }

    setPages(prev => prev.filter(p => p.id !== pageId));
    setPageData(prev => {
      const newData = { ...prev };
      delete newData[pageId];
      return newData;
    });

    // If we're deleting the current page, switch to the first remaining page
    if (currentPageId === pageId) {
      const remainingPages = pages.filter(p => p.id !== pageId);
      if (remainingPages.length > 0) {
        setCurrentPageId(remainingPages[0].id);
      }
    }

    toast.success(`Deleted page: ${pageToDelete.name}`);
  }, [pages, currentPageId]);

  const renamePage = useCallback((pageId: string, newName: string) => {
    if (!newName.trim()) {
      toast.error('Page name cannot be empty');
      return;
    }

    setPages(prev => prev.map(page => 
      page.id === pageId 
        ? { ...page, name: newName.trim(), updatedAt: new Date() }
        : page
    ));

    toast.success('Page renamed successfully');
  }, []);

  const switchToPage = useCallback((pageId: string) => {
    const targetPage = pages.find(p => p.id === pageId);
    if (!targetPage) {
      toast.error('Page not found');
      return;
    }

    setCurrentPageId(pageId);
    toast.success(`Switched to: ${targetPage.name}`);
  }, [pages]);

  const getCurrentPage = useCallback((): Page | undefined => {
    return pages.find(p => p.id === currentPageId);
  }, [pages, currentPageId]);

  const getCurrentPageData = useCallback(() => {
    return pageData[currentPageId] || { nodes: [], edges: [] };
  }, [pageData, currentPageId]);

  const updateCurrentPageData = useCallback((nodes: HtmlMockupNode[], edges: Edge[]) => {
    console.log('📝 updateCurrentPageData called with', nodes.length, 'nodes for page', currentPageId);
    setPageData(prev => {
      const newData = {
        ...prev,
        [currentPageId]: { nodes, edges }
      };
      console.log('📝 New page data:', newData);
      return newData;
    });
  }, [currentPageId]);

  return {
    pages,
    currentPageId,
    pageData,
    createPage,
    deletePage,
    renamePage,
    switchToPage,
    getCurrentPage,
    getCurrentPageData,
    updateCurrentPageData,
  };
}
